# Job Status Banner System

This document explains how to use the new dynamic job status banner system.

## Overview

The banner system supports three different states to reflect your ability to take on new work:

1. **Open** (Blue) - Accepting new customers
2. **Limited** (Yellow) - Busy, limiting new jobs 
3. **Closed** (Red) - Not accepting new jobs

## How to Change the Status

To change the current job status, edit the file `src/lib/jobStatus.js`:

```javascript
// Change this value to control the current job status banner
// Valid values: 'open', 'limited', 'closed'
export const CURRENT_JOB_STATUS = 'open'; // Change this to 'limited' or 'closed'
```

## Customizing Messages

To customize the messages and styling for each status, edit `src/lib/constants.js` in the `JOB_STATUS_CONFIG` section:

```javascript
export const JOB_STATUS_CONFIG = {
	open: {
		title: 'Job Status',
		message: '22nd August 2025 - We are taking on new customers, contact us now!',
		bgColor: 'bg-secondary',
		textColor: 'text-base-100',
		icon: 'rocket'
	},
	limited: {
		title: 'Job Status', 
		message: 'We are currently busy and limiting new jobs to October 2025',
		bgColor: 'bg-warning',
		textColor: 'text-warning-content',
		icon: 'clock'
	},
	closed: {
		title: 'Job Status',
		message: 'We are not accepting new jobs at this time',
		bgColor: 'bg-error',
		textColor: 'text-error-content',
		icon: 'magnifying-glass'
	}
};
```

## Available Icons

The system supports these icons:
- `rocket` - For open status
- `clock` - For limited status  
- `magnifying-glass` - For closed status

## DaisyUI Colors Used

- **Open**: `bg-secondary` with `text-base-100` (blue background, white text)
- **Limited**: `bg-warning` with `text-warning-content` (yellow background, dark text)
- **Closed**: `bg-error` with `text-error-content` (red background, white text)

## Files Modified

1. `src/lib/constants.js` - Added job status configuration
2. `src/lib/jobStatus.js` - Controls current status (NEW FILE)
3. `src/lib/components/JobStatusBanner.svelte` - Banner component (NEW FILE)
4. `src/routes/+layout.svelte` - Updated to use new banner component
