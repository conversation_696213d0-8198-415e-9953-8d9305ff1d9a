# Job Status Banner System

This document explains how to use the new dynamic job status banner system.

## Overview

The banner system supports three different states to reflect your ability to take on new work:

1. **Open** (Blue) - Accepting new customers
2. **Limited** (Yellow) - Busy, limiting new jobs 
3. **Closed** (Red) - Not accepting new jobs

## How to Change the Status

To change the current job status, edit the `CURRENT_JOB_STATUS` value in `src/lib/constants.js`:

```javascript
// Current Job Status - Change this value to control the banner
// Valid values: 'open', 'limited', 'closed'
export const CURRENT_JOB_STATUS = 'closed'; // Change this to 'open' or 'limited'
```

## Customizing Messages and Title

To customize the title and messages for each status, edit `src/lib/constants.js`:

```javascript
// Job Status Configuration
export const JOB_STATUS_TITLE = 'Update on 22nd August 2025';

export const JOB_STATUS_CONFIG = {
	open: {
		message: 'We are taking on new customers, contact us now to chat about your project',
		bgColor: 'bg-secondary',
		textColor: 'text-base-100',
		icon: 'rocket'
	},
	limited: {
		message: 'We are currently limiting take-up of new jobs to October 2025',
		bgColor: 'bg-yellow-400',
		textColor: 'text-warning-content',
		icon: 'clock'
	},
	closed: {
		message: 'To protect our current customers, we are not accepting any new jobs at this time',
		bgColor: 'bg-red-700',
		textColor: 'text-base-100',
		icon: 'magnifying-glass'
	}
};

// Current Job Status - Change this value to control the banner
// Valid values: 'open', 'limited', 'closed'
export const CURRENT_JOB_STATUS = 'closed';
```

## Available Icons

The system supports these icons:
- `rocket` - For open status
- `clock` - For limited status  
- `magnifying-glass` - For closed status

## DaisyUI Colors Used

- **Open**: `bg-secondary` with `text-base-100` (blue background, white text)
- **Limited**: `bg-warning` with `text-warning-content` (yellow background, dark text)
- **Closed**: `bg-error` with `text-error-content` (red background, white text)

## Files Modified

1. `src/lib/constants.js` - Added job status configuration and current status setting
2. `src/lib/components/JobStatusBanner.svelte` - Banner component (NEW FILE)
3. `src/routes/+layout.svelte` - Updated to use new banner component
4. `src/lib/jobStatus.js` - REMOVED (consolidated into constants.js)
