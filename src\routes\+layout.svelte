<script lang="ts">
	import '../app.css';
	import { afterNavigate } from '$app/navigation';
	import CookieConsent from '$lib/components/CookieConsent.svelte';

	let { children } = $props();

	// Track route changes
	afterNavigate((nav) => {
		if (typeof window !== 'undefined' && window.gtag) {
			window.gtag('config', 'G-BTF092ZXYF', {
				page_path: nav.to?.url.pathname
			});
		}
	});
</script>

<div class="app">
	<!-- Launch Banner -->
	<div class="bg-secondary px-4 py-2 text-base-100">
		<div class="flex items-center justify-center gap-2">
			<svg
				xmlns="http://www.w3.org/2000/svg"
				fill="none"
				viewBox="0 0 24 24"
				stroke-width="1.5"
				stroke="currentColor"
				class="size-5"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					d="M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"
				/>
			</svg>
			<p class="text-sm font-medium">
				Our official launch will be in September 2025 - stay tuned!
			</p>
		</div>
	</div>

	<main>
		{@render children()}
	</main>

	<!-- Cookie consent banner to be legally compliant -->
	<CookieConsent />
</div>

<style>
	.app {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}

	main {
		flex: 1;
		display: flex;
		flex-direction: column;
		padding: 1rem;
		width: 100%;
		max-width: 64rem;
		margin: 0 auto;
		box-sizing: border-box;
	}

	@media (min-width: 480px) {
		main {
			padding: 1rem;
		}
	}
</style>
